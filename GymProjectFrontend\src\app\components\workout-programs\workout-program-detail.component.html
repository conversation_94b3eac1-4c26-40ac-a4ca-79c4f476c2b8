<div class="workout-detail-container">
  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-state">
    <div class="loading-content">
      <div class="modern-spinner">
        <div class="spinner-ring"></div>
      </div>
      <h3 class="loading-title">Program Yükleniyor</h3>
      <p class="loading-subtitle">Lütfen bekleyiniz...</p>
    </div>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && program" class="workout-detail-content fade-in">
    <!-- Floating Back Button -->
    <button
      class="floating-back-btn"
      (click)="goBack()"
      aria-label="Geri dön"
      title="Program listesine geri dön">
      <fa-icon [icon]="faArrowLeft"></fa-icon>
    </button>

    <!-- Header Section -->
    <div class="workout-header">
      <div class="header-top">
        <div class="header-actions" *ngIf="isOwner || isAdmin">
          <button
            class="action-btn edit-btn"
            (click)="editProgram()"
            aria-label="Programı düzenle">
            <fa-icon [icon]="faEdit"></fa-icon>
            <span class="btn-text">Düzenle</span>
          </button>
          <button
            class="action-btn delete-btn"
            (click)="deleteProgram()"
            aria-label="Programı sil">
            <fa-icon [icon]="faTrashAlt"></fa-icon>
            <span class="btn-text">Sil</span>
          </button>
        </div>
      </div>

      <div class="program-title-section">
        <div class="program-icon">
          <fa-icon [icon]="faDumbbell"></fa-icon>
        </div>
        <div class="program-title-content">
          <h1 class="program-title">{{program.programName}}</h1>
          <p class="program-subtitle">Antrenman Programı Detayları</p>
        </div>
      </div>
    </div>

    <!-- Program Stats Overview -->
    <div class="program-stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <fa-icon [icon]="faCalendarAlt"></fa-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{program.dayCount || 0}}</div>
            <div class="stat-label">Toplam Gün</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <fa-icon [icon]="faDumbbell"></fa-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{getTotalExerciseCount()}}</div>
            <div class="stat-label">Toplam Egzersiz</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <fa-icon [icon]="faUsers"></fa-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{getWorkoutDayCount()}}</div>
            <div class="stat-label">Antrenman Günü</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <fa-icon [icon]="faClock"></fa-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{getRestDayCount()}}</div>
            <div class="stat-label">Dinlenme Günü</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Program Information -->
    <div class="program-info-section">
      <div class="info-cards-grid">
        <div class="info-card">
          <div class="info-card-header">
            <fa-icon [icon]="faChartLine" class="info-icon"></fa-icon>
            <h3 class="info-title">Deneyim Seviyesi</h3>
          </div>
          <div class="info-card-content">
            <span class="info-badge" [ngClass]="getExperienceLevelBadgeClass(program.experienceLevel)">
              {{program.experienceLevel || 'Belirtilmemiş'}}
            </span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-card-header">
            <fa-icon [icon]="faBullseye" class="info-icon"></fa-icon>
            <h3 class="info-title">Hedef</h3>
          </div>
          <div class="info-card-content">
            <span class="info-badge" [ngClass]="getTargetGoalBadgeClass(program.targetGoal)">
              {{program.targetGoal || 'Belirtilmemiş'}}
            </span>
          </div>
        </div>

        <div class="info-card">
          <div class="info-card-header">
            <fa-icon [icon]="faCalendar" class="info-icon"></fa-icon>
            <h3 class="info-title">Oluşturma Tarihi</h3>
          </div>
          <div class="info-card-content">
            <span class="info-text">{{formatDate(program.creationDate)}}</span>
          </div>
        </div>

        <div class="info-card full-width" *ngIf="program.description">
          <div class="info-card-header">
            <fa-icon [icon]="faFileAlt" class="info-icon"></fa-icon>
            <h3 class="info-title">Program Açıklaması</h3>
          </div>
          <div class="info-card-content">
            <p class="program-description">{{program.description}}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Program Days Section -->
    <div class="program-days-section">
      <div class="section-header">
        <div class="section-title">
          <fa-icon [icon]="faCalendarAlt" class="section-icon"></fa-icon>
          <h2>Program Günleri</h2>
        </div>
        <div class="section-subtitle">
          <span>{{program.dayCount}} günlük antrenman programı</span>
        </div>
      </div>

      <!-- Days Grid -->
      <div class="days-grid" *ngIf="program.days && program.days.length > 0">
        <div
          *ngFor="let day of program.days; let i = index"
          class="day-card"
          [class.rest-day]="day.isRestDay"
          [class.workout-day]="!day.isRestDay">

          <!-- Day Header -->
          <div class="day-header">
            <div class="day-info">
              <div class="day-number">Gün {{day.dayNumber}}</div>
              <div class="day-name">{{day.dayName}}</div>
            </div>
            <div class="day-badge">
              <span
                *ngIf="day.isRestDay"
                class="badge rest-badge">
                <fa-icon [icon]="faClock"></fa-icon>
                Dinlenme
              </span>
              <span
                *ngIf="!day.isRestDay"
                class="badge workout-badge">
                <fa-icon [icon]="faDumbbell"></fa-icon>
                {{day.exercises?.length || 0}} Egzersiz
              </span>
            </div>
          </div>

          <!-- Day Content -->
          <div class="day-content">
            <!-- Rest Day Content -->
            <div *ngIf="day.isRestDay" class="rest-day-content">
              <div class="rest-icon">
                <fa-icon [icon]="faClock"></fa-icon>
              </div>
              <h4>Dinlenme Günü</h4>
              <p>Bu gün vücudunuzun dinlenmesi ve toparlanması için ayrılmıştır.</p>
            </div>

            <!-- Workout Day Content -->
            <div *ngIf="!day.isRestDay && day.exercises && day.exercises.length > 0" class="workout-day-content">
              <div class="exercises-container">
                <div
                  *ngFor="let exercise of day.exercises; let j = index"
                  class="exercise-card">

                  <!-- Exercise Header -->
                  <div class="exercise-header">
                    <div class="exercise-number">{{j + 1}}</div>
                    <div class="exercise-name">{{exercise.exerciseName}}</div>
                    <!-- Checkbox placeholder for future use -->
                    <div class="exercise-checkbox-placeholder">
                      <!-- Checkbox will be added here in future -->
                    </div>
                  </div>

                  <!-- Exercise Details -->
                  <div class="exercise-details">
                    <div class="detail-item">
                      <fa-icon [icon]="faDumbbell" class="detail-icon"></fa-icon>
                      <span class="detail-label">Set:</span>
                      <span class="detail-value">{{exercise.sets}}</span>
                    </div>
                    <div class="detail-item">
                      <fa-icon [icon]="faUsers" class="detail-icon"></fa-icon>
                      <span class="detail-label">Tekrar:</span>
                      <span class="detail-value">{{exercise.reps}}</span>
                    </div>
                  </div>

                  <!-- Exercise Notes -->
                  <div *ngIf="exercise.notes" class="exercise-notes">
                    <fa-icon [icon]="faInfoCircle" class="note-icon"></fa-icon>
                    <span class="note-text">{{exercise.notes}}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty Workout Day -->
            <div *ngIf="!day.isRestDay && (!day.exercises || day.exercises.length === 0)" class="empty-workout-content">
              <div class="empty-icon">
                <fa-icon [icon]="faDumbbell"></fa-icon>
              </div>
              <h4>Egzersiz Eklenmemiş</h4>
              <p>Bu güne henüz egzersiz eklenmemiş. Düzenleme sayfasından egzersiz ekleyebilirsiniz.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!program.days || program.days.length === 0" class="empty-program-state">
        <div class="empty-icon">
          <fa-icon [icon]="faCalendarAlt"></fa-icon>
        </div>
        <h3>Program Günü Bulunamadı</h3>
        <p>Bu programa henüz gün eklenmemiş. Programa gün eklemek için düzenleme sayfasını kullanın.</p>
        <button
          *ngIf="isOwner || isAdmin"
          class="modern-btn modern-btn-primary"
          (click)="editProgram()">
          <fa-icon [icon]="faEdit" class="modern-btn-icon"></fa-icon>
          Programı Düzenle
        </button>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !program" class="error-state">
    <div class="error-content">
      <div class="error-icon">
        <fa-icon [icon]="faInfoCircle"></fa-icon>
      </div>
      <h3 class="error-title">Program Bulunamadı</h3>
      <p class="error-message">
        Aradığınız antrenman programı mevcut değil veya silinmiş olabilir.
        Lütfen program listesine geri dönerek başka bir program seçin.
      </p>
      <div class="error-actions">
        <button class="modern-btn modern-btn-primary" (click)="goBack()">
          <fa-icon [icon]="faArrowLeft" class="modern-btn-icon"></fa-icon>
          Program Listesine Dön
        </button>
      </div>
    </div>
  </div>
</div>
