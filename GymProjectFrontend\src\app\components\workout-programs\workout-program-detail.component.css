/* Modern Workout Program Detail Styles */

/* Container */
.workout-detail-container {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 1rem;
}

.workout-detail-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.loading-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.modern-spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 4px solid var(--primary-light);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.loading-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Header Section */
.workout-header {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  color: var(--text-primary);
  text-decoration: none;
  transition: all var(--transition-speed) var(--transition-timing);
  cursor: pointer;
}

.back-btn:hover {
  background: var(--bg-tertiary);
  transform: translateX(-2px);
  color: var(--primary);
}

.back-text {
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid;
  border-radius: var(--border-radius-md);
  background: transparent;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  font-weight: 500;
}

.edit-btn {
  border-color: var(--primary);
  color: var(--primary);
}

.edit-btn:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.delete-btn {
  border-color: var(--danger);
  color: var(--danger);
}

.delete-btn:hover {
  background: var(--danger);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-text {
  font-size: 0.9rem;
}

.program-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.program-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-light);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1.5rem;
}

.program-title-content {
  flex: 1;
}

.program-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.program-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}


/* Stats Section */
.program-stats-section {
  margin: 2rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-speed) var(--transition-timing);
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-light);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1.25rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Info Section */
.program-info-section {
  margin: 2rem 0;
}

.info-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.info-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  transition: all var(--transition-speed) var(--transition-timing);
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.info-card.full-width {
  grid-column: 1 / -1;
}

.info-card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.info-icon {
  color: var(--primary);
  font-size: 1.25rem;
}

.info-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.info-card-content {
  margin-left: 2rem;
}

.info-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  font-size: 0.9rem;
}

.info-text {
  color: var(--text-primary);
  font-size: 1rem;
}

.program-description {
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  font-size: 1rem;
}

/* Badge Color Classes */
.program-badge-primary,
.info-badge.program-badge-primary {
  background: var(--primary-light);
  color: var(--primary);
}

.program-badge-success,
.info-badge.program-badge-success {
  background: var(--success-light);
  color: var(--success);
}

.program-badge-warning,
.info-badge.program-badge-warning {
  background: var(--warning-light);
  color: var(--warning);
}

.program-badge-danger,
.info-badge.program-badge-danger {
  background: var(--danger-light);
  color: var(--danger);
}

.program-badge-info,
.info-badge.program-badge-info {
  background: var(--info-light);
  color: var(--info);
}

.program-badge-secondary,
.info-badge.program-badge-secondary {
  background: var(--secondary-light);
  color: var(--secondary);
}

/* Days Section */
.program-days-section {
  margin: 2rem 0;
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.section-icon {
  color: var(--primary);
  font-size: 1.5rem;
}

.section-title h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.section-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.day-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all var(--transition-speed) var(--transition-timing);
}

.day-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.day-card.rest-day {
  border-left: 4px solid var(--warning);
}

.day-card.workout-day {
  border-left: 4px solid var(--primary);
}

.day-header {
  background: var(--bg-secondary);
  padding: 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.day-info {
  flex: 1;
}

.day-number {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.day-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.day-badge {
  flex-shrink: 0;
}

.badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-size: 0.875rem;
  font-weight: 500;
}

.rest-badge {
  background: var(--warning-light);
  color: var(--warning);
}

.workout-badge {
  background: var(--primary-light);
  color: var(--primary);
}

.day-content {
  padding: 1.5rem;
}

/* Rest Day Content */
.rest-day-content,
.empty-workout-content {
  text-align: center;
  padding: 2rem 1rem;
}

.rest-icon,
.empty-icon {
  width: 60px;
  height: 60px;
  background: var(--warning-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: var(--warning);
  font-size: 1.5rem;
}

.empty-icon {
  background: var(--secondary-light);
  color: var(--secondary);
}

.rest-day-content h4,
.empty-workout-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.rest-day-content p,
.empty-workout-content p {
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* Workout Day Content */
.workout-day-content {
  padding: 0;
}

.exercises-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.exercise-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  transition: all var(--transition-speed) var(--transition-timing);
}

.exercise-card:hover {
  background: var(--bg-tertiary);
  transform: translateX(3px);
}

.exercise-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.exercise-number {
  width: 30px;
  height: 30px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.exercise-name {
  flex: 1;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.exercise-checkbox-placeholder {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: var(--bg-primary);
  /* Future checkbox will be placed here */
}

.exercise-details {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 0.75rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--bg-primary);
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.detail-icon {
  color: var(--primary);
  font-size: 0.875rem;
}

.detail-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-value {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 600;
}

.exercise-notes {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  background: var(--info-light);
  padding: 0.75rem;
  border-radius: var(--border-radius-sm);
  border-left: 3px solid var(--info);
}

.note-icon {
  color: var(--info);
  font-size: 0.875rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.note-text {
  color: var(--info);
  font-size: 0.875rem;
  line-height: 1.4;
  font-style: italic;
}

/* Empty States */
.empty-program-state {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
}

.empty-program-state .empty-icon {
  width: 80px;
  height: 80px;
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

.empty-program-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.75rem 0;
}

.empty-program-state p {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

/* Error State */
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-icon {
  width: 80px;
  height: 80px;
  background: var(--danger-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: var(--danger);
  font-size: 2rem;
}

.error-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
}

.error-message {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 2rem 0;
}

.error-actions {
  display: flex;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .days-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .workout-detail-container {
    padding: 0.75rem;
  }

  .workout-detail-content {
    gap: 1.5rem;
  }

  /* Header adjustments */
  .header-top {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .action-btn .btn-text {
    display: none;
  }

  .program-title-section {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .program-title {
    font-size: 1.5rem;
  }

  /* Stats adjustments */
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  /* Info cards adjustments */
  .info-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .info-card {
    padding: 1rem;
  }

  .info-card-content {
    margin-left: 1.5rem;
  }

  /* Days adjustments */
  .days-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .day-header {
    padding: 1rem;
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .day-content {
    padding: 1rem;
  }

  .exercise-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .detail-item {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .workout-detail-container {
    padding: 0.5rem;
  }

  .back-btn .back-text {
    display: none;
  }

  .program-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .program-title {
    font-size: 1.25rem;
  }

  .section-title {
    flex-direction: column;
    gap: 0.5rem;
  }

  .section-title h2 {
    font-size: 1.5rem;
  }

  .exercise-header {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .exercise-checkbox-placeholder {
    order: 3;
    width: 100%;
    height: 20px;
    margin-top: 0.5rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark Mode Specific Adjustments */
[data-theme="dark"] .workout-header {
  background: var(--bg-secondary);
}

[data-theme="dark"] .stat-card,
[data-theme="dark"] .info-card,
[data-theme="dark"] .day-card {
  background: var(--bg-secondary);
}

[data-theme="dark"] .day-header {
  background: var(--bg-tertiary);
}

[data-theme="dark"] .exercise-card {
  background: var(--bg-tertiary);
}

[data-theme="dark"] .exercise-card:hover {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .detail-item {
  background: var(--bg-secondary);
}

[data-theme="dark"] .empty-program-state,
[data-theme="dark"] .rest-day-content,
[data-theme="dark"] .empty-workout-content {
  background: var(--bg-secondary);
}
